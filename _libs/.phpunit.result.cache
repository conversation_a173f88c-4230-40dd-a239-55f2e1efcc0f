{"version": 1, "defects": {"Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetSupportedExtensions": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testSupportsFormat": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetMimeType": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetDefaultExtension": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatName": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatOptions": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFile": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithCustomDelimiter": 3, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithTabDelimiter": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFilePointer": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithUnsupportedType": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithInvalidFile": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithHeader": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithoutHeader": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithMetadata": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddRecord": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddMultipleRecords": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testCreateRecord": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordAtInvalidIndex": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testMetadataOperations": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIterator": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testToArray": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoading": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventDirectRecordOperations": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventCreateRecord": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetPageSize": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetHeader": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testValidate": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateCsvAdapter": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateExcelAdapter": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateJsonAdapter": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithOptions": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCaching": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithUnsupportedFormat": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormats": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormatsCaching": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testIsFormatSupported": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilename": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithoutExtension": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUnsupportedExtension": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testFormatTrimming": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIsEmptyWithLazyLoading": 3, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidDate": 3, "Tests\\Nzoom\\Export\\DataFactoryTest::testConstructor": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testSetChunkSize": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithEmptyModels": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithOutlookFields": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithModels": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testFieldTypeMapping": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithLargeModelSet": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreaming": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreaming": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testHeaderCreationWithDefaultValues": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testModelWithoutGetExportVarTypeMethod": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testComplexScenario": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCursorStreamingPaginationBehavior": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructor": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testMultipleTypesAndSections": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFilterVisibilityInitialization": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFirstOrZeroMethod": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithDifferentTypeAndSection": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithSessionData": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithPlugins": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithNoPlugins": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDefaultValues": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldTypes": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldRequirements": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testComplexScenario": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldCustomIds": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testTranslationIntegration": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testEmptyTypesAndSections": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructorWithNullModelNames": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testBaseExportOptionsStructure": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDelimiterOptions": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testGroupTablesOptions": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFormatOptions": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithFiltersHide": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleSection": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleType": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsBasic": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCreateExportAction": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testInvokeMethod": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelFactoryName": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelName": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCompleteWorkflow": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testConstructor": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithArray": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testComplexExportScenario": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithAjaxRequest": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithoutLogger": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithLogger": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithInvalidStream": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithValidStream": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateTempStream": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithEmptyString": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithExistingExtension": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithNull": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithString": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelName": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportWithInvalidFormat": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportSuccess": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportAction": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateGeneratorFileStreamer": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportData": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testIsFormatSupported": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetSupportedFormats": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetAdapter": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetFormatFactory": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetFormatFactory": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelFactoryName": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testLazyInitialization": 4, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper": 3, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidResource": 3, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testSendHeaders": 5, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testSendHeadersWithDefaultContentType": 5, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithLargeDataset": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testProcessExportData": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testCreateSpreadsheet": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testSetDocumentProperties": 3, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetFieldSpecificFormat": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testConvertCustomDateFormat": 3, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testConvertCustomNumberFormat": 3, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportXlsFormat": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithCustomOptions": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportToPhpOutput": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportToTempFile": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure": 3, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetError": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport": 3, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithUnwritableFile": 3, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithNullValues": 3, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToTempFile": 3, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetDecimalPlaces": 3, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithNullValues": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithInvalidJson": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportLogsCompletion": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithJsonOptions": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithConfiguration": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithFilePointer": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithMixedDataTypes": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithMetadata": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithNestedStructure": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithObjectStructure": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportToPhpOutput": 4, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportToTempFile": 4, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration": 3, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSendPreparedHeadersWithoutOutputBuffering": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithBasicGenerator": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testTimeIncrement": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testJsonDataGeneration": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCsvDataGeneration": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testMultipleStreamCalls": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testInheritsFromFileStreamer": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithUnicodeData": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithBinaryData": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithVeryLongChunks": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorFunctionReturnsArray": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorFunctionReturnsNull": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorFunctionThrowsException": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorWithException": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCacheHeaders": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithTotalSize": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCustomHeaders": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testHeadersIntegration": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testReset": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithLargeGenerator": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithNonStringChunks": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithNullChunks": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithEmptyGenerator": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithSimpleGenerator": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testSetTotalSizeToNull": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testSetTotalSize": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithInvalidGeneratorFunction": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithDefaultMimeType": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCleanupAfterException": 5, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testPerformStreamingDirectly": 5, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testAddNamedRangesWithInvalidVarNames": 4, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testAddNamedRanges": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithComplexFilters": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsPreserveFilterStructure": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingRecordProviderExecution": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithDefaultPageSize": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingPaginationBehavior": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration": 4}, "times": {"Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetSupportedExtensions": 0.009, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testSupportsFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetMimeType": 0, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetDefaultExtension": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatName": 0, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatOptions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFile": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithCustomDelimiter": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithTabDelimiter": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithUnsupportedType": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithInvalidFile": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithHeader": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithoutHeader": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithMetadata": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddMultipleRecords": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testCreateRecord": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordAtInvalidIndex": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testMetadataOperations": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIterator": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testToArray": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventDirectRecordOperations": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventCreateRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetPageSize": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetHeader": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testValidate": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateCsvAdapter": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateExcelAdapter": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateJsonAdapter": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithOptions": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCaching": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithUnsupportedFormat": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormats": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormatsCaching": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testIsFormatSupported": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilename": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithoutExtension": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUnsupportedExtension": 0.002, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testFormatTrimming": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordsWithLazyLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddRecordWithValidation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIsEmptyWithLazyLoading": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordAtWithLazyLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSortByColumnInvalidColumn": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testToArrayFormatted": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testFilter": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSortByColumnWithNullValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSortByColumn": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testCountWithEagerLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithMinimalParameters": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testAddStyleOverwritesExisting": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testAllValidTypes": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testCreateValueWithDifferentTypes": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testCreateValue": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithInvalidArray": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidArray": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithInvalidDate": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidDate": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidBoolean": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidFloat": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithInvalidInteger": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidInteger": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testAddStyle": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithAllParameters": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetStyles": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetWidthWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetWidth": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetFormat": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetTypeWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetLabelWithEmptyString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetLabel": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetVarNameWithEmptyString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetVarName": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithEmptyVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testComplexStyling": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testConstructorWithDefaults": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetVarNames": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testCountableImplementation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testIteratorWithEmptyHeader": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testIteratorImplementation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testValidateRecordInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testValidateRecordInvalidCount": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testValidateRecordValid": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumnsWithInvalidVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumnsWithEmptyArray": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumnsPartialOrder": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumns": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetTypes": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetLabels": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testConstructorWithParameters": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddStyleOverwritesExisting": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddStyle": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testSetStyles": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testSetBackgroundColor": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetColumnByVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetColumnAt": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetColumns": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testHasColumn": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddColumnWithDuplicateVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddMultipleColumns": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddColumn": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testConstructorWithDefaults": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetValueByColumnName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testIteratorWithEmptyRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testIteratorImplementation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testCountableImplementation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testValidateWithInvalidRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testValidateWithValidRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetMetadata": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testMetadataOperations": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetFormattedValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetRawValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testHasValue": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetValueAt": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testConstructorWithMetadata": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueByColumnNameNonExistent": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueByColumnName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAtNegativeIndex": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAtOutOfRange": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAtWithExportValueObject": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAt": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testAddMultipleValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testAddValueWithExportValueObject": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testAddValue": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testConstructorWithMinimalParameters": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateTimeWithString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueWithNullType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateWithDateTime": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateWithCustomFormat": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateWithString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateTimeWithDateTime": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateTimeWithCustomFormat": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueOtherTypes": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateArrayType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithArray": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeAndDateType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeAndDateTimeType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeAndCustomFormat": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeNoType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithPrimitiveTypes": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateDateTimeType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testConstructorWithAllParameters": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetFormat": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testConstructorWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetValidTypes": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetValue": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetTypeWithNull": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetTypeWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetFormatWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateDateType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testIsNull": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateWithNullValue": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateWithNullType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateStringType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateIntegerType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateFloatType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateBooleanType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testConstructor": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testSetChunkSize": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithEmptyModels": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithOutlookFields": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithModels": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testFieldTypeMapping": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithLargeModelSet": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreaming": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreaming": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testHeaderCreationWithDefaultValues": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testModelWithoutGetExportVarTypeMethod": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithDefaultPageSize": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithComplexFilters": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsPreserveFilterStructure": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingRecordProviderExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingPaginationBehavior": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCursorStreamingPaginationBehavior": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructor": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testMultipleTypesAndSections": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFilterVisibilityInitialization": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFirstOrZeroMethod": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithDifferentTypeAndSection": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithSessionData": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithPlugins": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithNoPlugins": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDefaultValues": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldTypes": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldRequirements": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldCustomIds": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testTranslationIntegration": 0.004, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testEmptyTypesAndSections": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructorWithNullModelNames": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testBaseExportOptionsStructure": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDelimiterOptions": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testGroupTablesOptions": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFormatOptions": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithFiltersHide": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleSection": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleType": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsBasic": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCreateExportAction": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testInvokeMethod": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelFactoryName": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelName": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCompleteWorkflow": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testConstructor": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithArray": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testComplexExportScenario": 0.002, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithAjaxRequest": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithoutLogger": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithLogger": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithInvalidStream": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithValidStream": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateTempStream": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithEmptyString": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithExistingExtension": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithNull": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithString": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelName": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportWithInvalidFormat": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportSuccess": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportAction": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateGeneratorFileStreamer": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportData": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testIsFormatSupported": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetSupportedFormats": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetAdapter": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetFormatFactory": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetFormatFactory": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelFactoryName": 0, "Tests\\Nzoom\\Export\\ExportServiceTest::testLazyInitialization": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUppercaseExtension": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testAdapterInstancesAreProperlyConfigured": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testFactoryWithDifferentModuleAndController": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testIsFormatSupportedWithWhitespace": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testIsFormatSupportedWithEmptyString": 0, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testSupportedFormatsAreUnique": 0, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testSupportedFormatsContainsAllExpectedFormats": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOnlyExtension": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithEmptyFilename": 0.002, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetClassNameFromFile": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithComplexPath": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testMultipleFormatSupport": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testEmptyOptionsHandling": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testAdapterConfiguration": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCacheKeyGeneration": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetAdapterClass": 0.002, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testIsValidAdapter": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testAdapterDiscoveryCaching": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testAdapterDiscovery": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testComplexCachingScenario": 0.002, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testConstructor": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTemp": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetRecordHeadersWithEmptyArray": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetFormatOptions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePath": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidDirectory": 0.002, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpOutput": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpMemory": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithPhpTempWithParams": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetRecordHeadersWithArray": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithUnsupportedWrapper": 0.002, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithValidFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithReadOnlyFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidResource": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithWrongResourceType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testConcreteImplementationMethods": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testExportMethod": 0.003, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetRecordHeadersWithUnsupportedType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetVarsMethod": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testConstructorWithoutTranslator": 0, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyString": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testSetConfiguration": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testSetConfigurationMerging": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithString": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithArray": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithEmptyArray": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithNull": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithDefaultExtension": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetRecordHeadersWithGetAllMethod": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithExistingExtension": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testGetExportFilenameWithDifferentExtension": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testSendHeaders": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testSendHeadersWithDefaultContentType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testHandleExportErrorWithLogger": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testHandleExportErrorWithoutLogger": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testHandleExportErrorWithAjaxRequest": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testPrivateMethodAccessibility": 0.001, "Tests\\Nzoom\\Export\\Adapter\\AbstractExportFormatAdapterTest::testValidateAndPrepareSaveTargetWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetSupportedExtensions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValueWithCustomFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithLargeDataset": 0.096, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testSetCellValueWithFormatting": 0.002, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testProcessExportRecord": 0.002, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testStyleHeaderRow": 0.002, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testAddHeaders": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testProcessExportData": 0.016, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testCreateSpreadsheet": 0.015, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testSetSpreadsheetLocale": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testSetDocumentProperties": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testOptimizeMemoryForExport": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetFieldSpecificFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testConvertCustomDateFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testConvertCustomNumberFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetExcelFormatFromExportValue": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testSupportsFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testCreateWriterWithUnsupportedFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testCreateWriter": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExtractSizingOptionsWithDefaults": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExtractSizingOptions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportXlsFormat": 0.032, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithCustomOptions": 0.026, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportToPhpOutput": 0.022, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportToTempFile": 0.024, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithUnsupportedType": 0.002, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithPhpSpreadsheetNotAvailable": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetFormatOptions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetFormatName": 0, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetDefaultExtension": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetMimeType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters": 0.022, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testGetExcelFormatting": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithoutLogger": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellError": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithComplexException": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithDifferentCellPositions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithRecordInformation": 0.002, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorPreservesOtherCells": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorWithEmptyException": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleExportRecordCellErrorIntegrationWithProcessExportRecord": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorResponseStructure": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithPhpSpreadsheetException": 0.002, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithComplexException": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithDifferentFilenames": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetError": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithoutLogger": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithEmptyException": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorWithAjaxRequest": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testFormatDateValue": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testFormatValueForCsv": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testNormalizeDelimiter": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportLogsProgress": 0.118, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithConfiguration": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithUnwritableFile": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithCustomEnclosure": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithNullValues": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithMixedDataTypes": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithBom": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithPipeDelimiter": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToPhpOutput": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToTempFile": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetDecimalPlaces": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testGetSupportedExtensions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithNullValues": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testFormatDateValue": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testFormatValueForJson": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithInvalidJson": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportLogsCompletion": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithJsonOptions": 0.002, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithConfiguration": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithUnwritableFile": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithMixedDataTypes": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testSupportsFormat": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithMetadata": 0.002, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithNestedStructure": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithObjectStructure": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportToPhpOutput": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportToTempFile": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testExportWithUnsupportedType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testGetFormatOptions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testGetFormatName": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testGetDefaultExtension": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testGetMimeType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\JsonExportFormatAdapterTest::testGetJsonOptions": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testAddHeader": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testPrepareCacheHeadersWithETag": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testCacheHeadersWithZeroExpiration": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHeadersWithSpecialCharacters": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHeadersWithEmptyValues": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSendPreparedHeadersWhenHeadersAlreadySent": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSend304NotModified": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithNullParameters": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithNoClientHeaders": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithLastModifiedNewer": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithETagMismatch": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithETagMatch": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testPrepareCacheHeadersWithLastModified": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testPrepareCacheHeadersWithCache": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testAddMultipleHeaders": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testPrepareCacheHeadersNoCache": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSetFileContentHeaders": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testClear": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testRemoveNonExistentHeader": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testRemoveHeader": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHasHeader": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testGetAllEmpty": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testGetAll": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testGetHeader": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSetHeaders": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testAddHeaderOverwritesExisting": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithInvalidIfModifiedSince": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSendPreparedHeadersWithOutputBuffering": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSendPreparedHeadersWithNoHeaders": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testFileContentHeadersWithOnlyExtension": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSend304NotModifiedWithNoCache": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSendPreparedHeadersWithoutOutputBuffering": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testComplexCacheValidationScenario": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testCacheValidationWithStrtoimeFailure": 0, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testHeaderCaseSensitivity": 0.001, "Tests\\Nzoom\\Export\\Streamer\\StreamHeadersTest::testSetHeadersWithEmptyArray": 0, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithBasicGenerator": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testTimeIncrement": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testJsonDataGeneration": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCsvDataGeneration": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testMultipleStreamCalls": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testInheritsFromFileStreamer": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithUnicodeData": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithBinaryData": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithVeryLongChunks": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorFunctionReturnsArray": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorFunctionReturnsNull": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorFunctionThrowsException": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testGeneratorWithException": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCacheHeaders": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithTotalSize": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCustomHeaders": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testHeadersIntegration": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testReset": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithLargeGenerator": 0.005, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithNonStringChunks": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithNullChunks": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithEmptyGenerator": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testStreamWithSimpleGenerator": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testSetTotalSizeToNull": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testSetTotalSize": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithInvalidGeneratorFunction": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testConstructorWithDefaultMimeType": 0.001, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testCleanupAfterException": 0.002, "Tests\\Nzoom\\Export\\Streamer\\GeneratorFileStreamerTest::testPerformStreamingDirectly": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithCustomDateFormats": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportValueFormatTakesPrecedence": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithDateFormatsFromConfiguration": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithUSDateFormats": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testDateFormatSettersAndGetters": 0.001, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testAddNamedRangesWithInvalidVarNames": 0.002, "Tests\\Nzoom\\Export\\Adapter\\ExcelExportFormatAdapterTest::testAddNamedRanges": 0.019}}